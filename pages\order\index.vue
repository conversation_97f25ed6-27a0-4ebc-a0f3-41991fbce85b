<template>
  <div class="h-[calc(100vh-56px)] flex flex-col mx-2">
    <!-- Header -->
    <div class="flex-shrink-0 my-1">
      <SearchOrder
        :dataEmployee="diaryStore.dataEmployee"
        :dataPaymentMethod="dataPaymentMethod"
        :dataOrderStatus="dataOrderStatus"
        @handleSearch="handleSearch"
      />

      <div class="flex justify-between items-center pb-2">
        <TabChangeOrder :tabs="tabs" @toogleTab="handleSetTab" />

        <button
          @click="handleNavigate"
          class="flex items-center gap-2 bg-primary text-white px-3 py-1 rounded-md transition duration-200"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          Tạo đơn
        </button>
      </div>

      <!-- <PERSON><PERSON><PERSON> bá<PERSON> không tìm thấy đơn -->
      <div v-if="ordersStore.isAlert" class="text-sm text-red-500 mt-2">
        Không tìm thấy đơn hàng
      </div>
    </div>

    <!-- Desktop Table Container -->
    <div class="hidden md:block flex-1 min-h-0">
      <div
        class="h-full flex flex-col bg-white rounded-lg border border-gray-200"
      >
        <!-- Loading toàn trang -->
        <LoadingDiary v-if="loading" :isPageOrder="true" />

        <!-- Table Content -->
        <div
          v-else-if="ordersStore.dataListOrder?.length || isLoading"
          class="flex-1 overflow-hidden"
        >
          <div
            class="h-full overflow-y-auto"
            style="scrollbar-width: thin; scrollbar-color: #d1d5db #f3f4f6"
          >
            <table class="table-auto w-full text-sm">
              <thead class="sticky top-0 z-10 bg-blue-50 border-b">
                <tr class="text-left font-semibold shadow-sm">
                  <th class="p-3 w-1/12 text-center">Mã đơn</th>
                  <th class="p-3 w-2/12">Khách hàng</th>
                  <th class="p-3 w-2/12">Nhân viên</th>
                  <th class="p-3 w-4/12 text-center">Sản phẩm</th>
                  <th class="p-3 w-1/12">Tổng giảm</th>
                  <th class="p-3 w-2/12">Thanh toán</th>
                  <th class="p-3 w-auto text-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="w-5 h-5 mx-auto"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                      />
                    </svg>
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- Loading State -->
                <tr v-if="isLoading">
                  <td colspan="7" class="p-0">
                    <OrderTableLoading :item-count="pagination.itemsPerPage" />
                  </td>
                </tr>

                <!-- Table Rows -->
                <tr
                  v-else
                  v-for="diary in ordersStore.dataListOrder"
                  :key="diary.id"
                  class="hover:bg-gray-50 divide-gray-200 even:bg-gray-50 odd:bg-white border-b border-gray-100"
                >
                  <TableDiary
                    :diary="diary"
                    :isNotDraft="true"
                    :isFFM="true"
                    :data="data"
                    @handleLoading="toggleLoading"
                  />
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Desktop Empty State -->
        <div v-else class="flex items-center justify-center h-full">
          <div class="text-center py-12">
            <svg
              class="w-12 h-12 text-gray-400 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">
              Chưa có đơn hàng nào
            </h3>
            <p class="text-gray-500">Hiện tại chưa có đơn hàng nào được tạo</p>
          </div>
        </div>

        <!-- Desktop Pagination -->
        <UiPagination
          v-if="ordersStore.dataListOrder?.length || isLoading"
          :current-page="pagination.currentPage"
          :total-pages="pagination.totalPages"
          :total-items="pagination.totalItems"
          :items-per-page="pagination.itemsPerPage"
          item-label="đơn hàng"
          @page-change="handlePageChange"
        />
      </div>
    </div>

    <!-- Mobile Cards Container -->
    <div class="md:hidden flex-1 min-h-0 overflow-hidden">
      <div class="h-full flex flex-col">
        <!-- Loading toàn trang mobile -->
        <LoadingDiary v-if="loading" :isPageOrder="true" />

        <!-- Mobile Cards List -->
        <div
          v-else
          class="flex-1 overflow-y-auto overflow-x-hidden py-2"
          style="scrollbar-width: thin; scrollbar-color: #d1d5db #f3f4f6"
        >
          <!-- Mobile Loading -->
          <div v-if="isLoading" class="space-y-2">
            <div
              v-for="n in pagination.itemsPerPage"
              :key="n"
              class="bg-white rounded-lg p-4 animate-pulse"
            >
              <div class="space-y-3">
                <div class="h-4 bg-gray-200 rounded w-1/3"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                <div class="h-3 bg-gray-200 rounded w-full"></div>
                <div class="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          </div>

          <!-- Mobile Cards -->
          <div
            v-else-if="ordersStore.dataListOrder?.length > 0"
            class="space-y-3"
          >
            <QuickOrder
              v-for="item in ordersStore.dataListOrder"
              :key="item.id"
              :dataOrder="item"
              :data="data"
              @handleLoading="toggleLoading"
            />
          </div>

          <!-- Mobile Empty State -->
          <div
            v-else-if="!isLoading"
            class="flex items-center justify-center h-full"
          >
            <div class="text-center py-12">
              <svg
                class="w-12 h-12 text-gray-400 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                Chưa có đơn hàng nào
              </h3>
              <p class="text-gray-500 mb-4">
                Hiện tại chưa có đơn hàng nào được tạo
              </p>
            </div>
          </div>
        </div>

        <!-- Mobile Pagination -->
        <UiPagination
          v-if="ordersStore.dataListOrder?.length || isLoading"
          :current-page="pagination.currentPage"
          :total-pages="pagination.totalPages"
          :total-items="pagination.totalItems"
          :items-per-page="pagination.itemsPerPage"
          item-label="đơn hàng"
          :show-items-info="false"
          min-height="70px"
          @page-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const TableDiary = defineAsyncComponent(
  () => import("~/components/Diary/TableDiary.vue")
);
const QuickOrder = defineAsyncComponent(
  () => import("~/components/Order/QuickOrder.vue")
);
const OrderTableLoading = defineAsyncComponent(
  () => import("~/components/ui/feedback/OrderTableLoading.vue")
);

import { isFriday } from "date-fns";
// Import UI components
import UiPagination from "~/components/ui/navigation/Pagination.vue";

// SEO
useHead({
  title: "Danh sách đơn hàng",
  meta: [{ name: "description", content: "Danh sách đơn hàng" }],
});

// PAGE META
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Đơn hàng",
});

// STORE
const ordersStore = useOrdersStore();
const diaryStore = useDiariesStore();
const router = useRouter();

// STATE
const isReturnOrder = ref(false);
const isLoading = ref(false);
const loading = ref(false);
const loadingNavigate = ref(false);

// PAGINATION STATE
const pagination = reactive({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 10,
});

// OPTIONS
const orderOptions = reactive({
  currentPage: 1,
  maxResult: 10,
  status_ignore: [1],
});

// TABS
const tabs = ref([
  { label: "Đơn Bán hàng", value: "SELL" },
  { label: "Đơn trả hàng", value: "RETURN" },
]);

// SEARCH & FILTER DATA
const dataPaymentMethod = ref();
const dataOrderStatus = ref();

// FETCH COMPOSABLES
const {
  fetchListSaleOrderStatus,
  fetchListSellOrder,
  fetchListSellOrderReturn,
} = useOrder();
const { getPaymentMethodTypes } = usePayment();

// LAZY LOADING SETTING DATA
const { data } = await useFetch("/data/setting.json");

//  LOAD DATA EMPLOYEE + STATUS + PAYMENT
async function fetchSearchMeta() {
  try {
    dataPaymentMethod.value = await getPaymentMethodTypes();
    const response = await fetchListSaleOrderStatus();
    dataOrderStatus.value = response?.data;
  } catch (error) {
    console.error("Error loading search metadata", error);
  }
}

// � RESET PAGINATION STATE
function resetPagination() {
  pagination.currentPage = 1;
  pagination.totalPages = 1;
  pagination.totalItems = 0;
  orderOptions.currentPage = 1;
}

// �📄 LOAD ORDER LIST WITH PAGINATION
async function loadOrders(page: number = 1) {
  if (isLoading.value) return;

  isLoading.value = true;

  try {
    // Update order options with current page
    orderOptions.currentPage = page;

    // Call API directly to get full response with pagination info
    const fetchFn = isReturnOrder.value
      ? fetchListSellOrderReturn
      : fetchListSellOrder;

    const response = await fetchFn(orderOptions);

    // Debug: Log full response structure
    console.log("🔍 Full API Response:", {
      response,
      data: response.data,
      dataData: response.data?.data,
      total: response.data?.total,
      pagination: response.data?.pagination,
      keys: response.data ? Object.keys(response.data) : [],
    });

    // Update store data
    ordersStore.dataListOrder = response.data?.data || [];

    // Update alert state
    ordersStore.isAlert = (response.data?.data || []).length === 0;

    // Update pagination info from API response
    const currentData = ordersStore.dataListOrder || [];
    pagination.currentPage = page;

    // Try to get pagination info from different possible response structures
    if (response.data?.total !== undefined) {
      // If API returns total count - this is the main case
      pagination.totalItems = Math.max(0, response.data.total);
      pagination.totalPages = Math.max(
        1,
        Math.ceil(response.data.total / pagination.itemsPerPage)
      );
    } else if (response.data?.pagination) {
      // If API returns pagination metadata
      pagination.totalItems = Math.max(
        0,
        response.data.pagination.totalItems || 0
      );
      pagination.totalPages = Math.max(
        1,
        response.data.pagination.totalPages || 1
      );
    } else if (response.total !== undefined) {
      // Total at root level
      pagination.totalItems = Math.max(0, response.total);
      pagination.totalPages = Math.max(
        1,
        Math.ceil(response.total / pagination.itemsPerPage)
      );
    } else if (response.pagination) {
      // Pagination at root level
      pagination.totalItems = Math.max(0, response.pagination.totalItems || 0);
      pagination.totalPages = Math.max(1, response.pagination.totalPages || 1);
    } else {
      // Fallback: Conservative estimation based on current data
      // This should only be used if API doesn't provide total count
      if (currentData.length === 0) {
        // No data at all
        pagination.totalItems = 0;
        pagination.totalPages = 1;
      } else if (currentData.length < pagination.itemsPerPage) {
        // We got less than a full page, so this is likely the last page
        pagination.totalPages = Math.max(1, page);
        pagination.totalItems = Math.max(
          0,
          (page - 1) * pagination.itemsPerPage + currentData.length
        );
      } else {
        // We got a full page, so there might be more data
        // Conservative estimate: assume at least one more page exists
        pagination.totalPages = Math.max(1, page + 1);
        pagination.totalItems = Math.max(1, page * pagination.itemsPerPage + 1);
      }
    }

    // Ensure current page is within valid range
    pagination.currentPage = Math.min(Math.max(1, page), pagination.totalPages);

    console.log("📦 Loaded orders for page", {
      page,
      totalItems: pagination.totalItems,
      totalPages: pagination.totalPages,
      ordersCount: currentData.length,
      responseStructure: {
        hasTotal: response.data?.total !== undefined,
        hasPagination: !!response.data?.pagination,
        dataLength: currentData.length,
      },
    });
  } catch (error) {
    console.error("Error loading orders", error);
    ordersStore.isAlert = true;
    // Reset pagination on error
    pagination.totalItems = 0;
    pagination.totalPages = 1;
    pagination.currentPage = 1;
  } finally {
    isLoading.value = false;
  }
}

// 📄 HANDLE PAGE CHANGE
async function handlePageChange(page: number) {
  if (isLoading.value) return;

  // Validate page number
  if (page < 1 || (pagination.totalPages > 0 && page > pagination.totalPages)) {
    console.warn(
      `Invalid page number: ${page}. Valid range: 1-${pagination.totalPages}`
    );
    return;
  }

  await loadOrders(page);
}

// 🔍 HANDLE SEARCH ACTION
async function handleSearch(filters: Record<string, any>) {
  if (loading.value) return;
  loading.value = true;

  // Reset pagination state
  pagination.currentPage = 1;
  pagination.totalPages = 1;
  pagination.totalItems = 0;

  Object.assign(orderOptions, {
    currentPage: 1,
    date_create_to: filters?.date_create_to,
    date_create_from: filters?.date_create_from,
    employee_assign: filters?.employee_assign,
    keyword: filters?.keyword,
    payment_method: filters?.payment_method,
    customer_multi_value: filters?.customer_multi_value,
    product_multi_value: filters?.product_multi_value,
    status: [filters?.status],
    exist_ffm_status: filters?.exist_ffm_status,
    ffm_status: filters?.ffm_status,
  });

  await loadOrders(1);
  loading.value = false;
}

// 🧭 HANDLE NAVIGATE TO CREATE ORDER
async function handleNavigate() {
  //
  isFriday
  //
  let isTimeout = false;
  const timer = setTimeout(() => {
    isTimeout = true;
    loadingNavigate.value = true;
  }, 150);

  // Use tab-isolated context instead of cookies

  await router.push(`/sale?orgId=${orgId.value}&storeId=${storeId.value}`);
  clearTimeout(timer);

  if (isTimeout) loadingNavigate.value = false;
}

// 🔀 HANDLE TAB SWITCH
async function handleSetTab(tab: string) {
  isReturnOrder.value = tab === "RETURN";

  // Reset pagination state
  pagination.currentPage = 1;
  pagination.totalPages = 1;
  pagination.totalItems = 0;
  orderOptions.currentPage = 1;
  loading.value = true;

  await loadOrders(1);
  loading.value = false;
}

// 🔁 INIT PAGE
onMounted(async () => {
  ordersStore.tooltip = null;
  localStorage.setItem("paymentAmount", "0");

  loading.value = true;
  await Promise.allSettled([
    handleSetTab("SELL"),
    diaryStore.handleGetDataEmployee(),
    fetchSearchMeta(),
  ]);
  loading.value = false;
});

// 🔄 TRIGGER LOADING FROM CHILD
const toggleLoading = (state: boolean) => {
  loadingNavigate.value = state;
};
</script>

<style scoped>
/* Custom scrollbar styles for webkit browsers */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Table optimizations */
.table-auto {
  table-layout: auto;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .space-y-3 > * + * {
    margin-top: 0.75rem;
  }
}

/* Animation for loading skeleton */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
