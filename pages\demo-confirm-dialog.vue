<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">Demo Optimized ConfirmDialog</h1>
      
      <!-- Variant Demos -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Default Variant -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4">Default Variant</h3>
          <button
            @click="showDialog('default')"
            class="w-full px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            Show Default Dialog
          </button>
        </div>

        <!-- Danger Variant -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4">Danger Variant</h3>
          <button
            @click="showDialog('danger')"
            class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Show Danger Dialog
          </button>
        </div>

        <!-- Warning Variant -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4">Warning Variant</h3>
          <button
            @click="showDialog('warning')"
            class="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            Show Warning Dialog
          </button>
        </div>

        <!-- Success Variant -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4">Success Variant</h3>
          <button
            @click="showDialog('success')"
            class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Show Success Dialog
          </button>
        </div>

        <!-- Info Variant -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4">Info Variant</h3>
          <button
            @click="showDialog('info')"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Show Info Dialog
          </button>
        </div>

        <!-- Custom Props -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4">Custom Props</h3>
          <button
            @click="showCustomDialog"
            class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Show Custom Dialog
          </button>
        </div>
      </div>

      <!-- Settings -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Dialog Settings</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Title:</label>
            <input
              v-model="customTitle"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Custom title"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Message:</label>
            <textarea
              v-model="customMessage"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              rows="2"
              placeholder="Custom message"
            ></textarea>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Confirm Text:</label>
            <input
              v-model="customConfirmText"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Confirm button text"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Cancel Text:</label>
            <input
              v-model="customCancelText"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Cancel button text"
            />
          </div>
        </div>
        
        <div class="mt-4 space-y-2">
          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="hideButton"
              class="mr-2 rounded border-gray-300 text-primary focus:ring-primary"
            />
            Hide Cancel Button
          </label>
          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="allowBackdropClose"
              class="mr-2 rounded border-gray-300 text-primary focus:ring-primary"
            />
            Allow Backdrop Close
          </label>
          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="autoFocus"
              class="mr-2 rounded border-gray-300 text-primary focus:ring-primary"
            />
            Auto Focus
          </label>
        </div>
      </div>

      <!-- Event Log -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">Event Log</h2>
        <div class="bg-gray-50 rounded p-4 max-h-64 overflow-y-auto">
          <div v-if="eventLog.length === 0" class="text-gray-500 text-sm">
            No events yet...
          </div>
          <div v-else class="space-y-2">
            <div
              v-for="(event, index) in eventLog"
              :key="index"
              class="text-sm border-b border-gray-200 pb-2"
            >
              <span class="text-gray-500 text-xs">{{ event.time }}</span>
              <span class="ml-2 font-medium">{{ event.event }}</span>
              <span class="ml-2 text-gray-600">{{ event.details }}</span>
            </div>
          </div>
        </div>
        <button
          @click="clearLog"
          class="mt-4 px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
        >
          Clear Log
        </button>
      </div>
    </div>

    <!-- Dialog Components -->
    <ConfirmDialog
      v-if="dialogState.default"
      title="Default Confirmation"
      message="This is a default confirmation dialog. Do you want to proceed?"
      @confirm="handleConfirm('default')"
      @cancel="handleCancel('default')"
    />

    <ConfirmDialog
      v-if="dialogState.danger"
      variant="danger"
      title="Delete Confirmation"
      message="Are you sure you want to delete this item? This action cannot be undone."
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="handleConfirm('danger')"
      @cancel="handleCancel('danger')"
    />

    <ConfirmDialog
      v-if="dialogState.warning"
      variant="warning"
      message="This action may have unintended consequences. Please review before proceeding."
      @confirm="handleConfirm('warning')"
      @cancel="handleCancel('warning')"
    />

    <ConfirmDialog
      v-if="dialogState.success"
      variant="success"
      title="Success!"
      message="Operation completed successfully. Would you like to continue?"
      @confirm="handleConfirm('success')"
      @cancel="handleCancel('success')"
    />

    <ConfirmDialog
      v-if="dialogState.info"
      variant="info"
      message="Here's some important information you should know about."
      @confirm="handleConfirm('info')"
      @cancel="handleCancel('info')"
    />

    <ConfirmDialog
      v-if="dialogState.custom"
      :variant="currentVariant"
      :title="customTitle"
      :message="customMessage"
      :confirm-text="customConfirmText"
      :cancel-text="customCancelText"
      :is-hide-button="hideButton"
      :allow-backdrop-close="allowBackdropClose"
      :auto-focus="autoFocus"
      @confirm="handleConfirm('custom')"
      @cancel="handleCancel('custom')"
      @backdrop-click="handleBackdropClick"
    />
  </div>
</template>

<script setup>
const ConfirmDialog = defineAsyncComponent(
  () => import("~/components/dialog/ConfirmDialog.vue")
);

// SEO
useHead({
  title: "Demo Optimized ConfirmDialog",
  meta: [{ name: "description", content: "Demo optimized confirm dialog component" }],
});

// State
const dialogState = reactive({
  default: false,
  danger: false,
  warning: false,
  success: false,
  info: false,
  custom: false
});

const eventLog = ref([]);
const currentVariant = ref('default');

// Custom settings
const customTitle = ref('Custom Title');
const customMessage = ref('This is a custom message with\nmultiple lines.');
const customConfirmText = ref('Proceed');
const customCancelText = ref('Go Back');
const hideButton = ref(false);
const allowBackdropClose = ref(true);
const autoFocus = ref(true);

// Methods
const addToLog = (event, details = '') => {
  eventLog.value.unshift({
    time: new Date().toLocaleTimeString(),
    event,
    details
  });
  
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50);
  }
};

const showDialog = (variant) => {
  dialogState[variant] = true;
  addToLog(`Show ${variant} dialog`);
};

const showCustomDialog = () => {
  currentVariant.value = 'warning';
  dialogState.custom = true;
  addToLog('Show custom dialog', `Variant: ${currentVariant.value}`);
};

const handleConfirm = (variant) => {
  dialogState[variant] = false;
  addToLog(`Confirmed ${variant} dialog`);
  useNuxtApp().$toast.success(`${variant} dialog confirmed!`);
};

const handleCancel = (variant) => {
  dialogState[variant] = false;
  addToLog(`Cancelled ${variant} dialog`);
  useNuxtApp().$toast.info(`${variant} dialog cancelled`);
};

const handleBackdropClick = () => {
  addToLog('Backdrop clicked', 'Dialog closed via backdrop');
};

const clearLog = () => {
  eventLog.value = [];
};

// Initialize
onMounted(() => {
  addToLog('Demo page loaded');
});
</script>

<style scoped>
/* Custom styles if needed */
</style>
