Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2116E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFB6A0  00021006A525 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA22870000 ntdll.dll
7FFA21210000 KERNEL32.DLL
7FFA1FF60000 KERNELBASE.dll
7FFA22670000 USER32.dll
7FFA1F980000 win32u.dll
7FFA20E40000 GDI32.dll
7FFA1F9B0000 gdi32full.dll
7FFA1FCC0000 msvcp_win.dll
7FFA20340000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA22100000 advapi32.dll
7FFA20B90000 msvcrt.dll
7FFA20E70000 sechost.dll
7FFA20460000 bcrypt.dll
7FFA20F70000 RPCRT4.dll
7FFA1E130000 CRYPTBASE.DLL
7FFA1FAD0000 bcryptPrimitives.dll
7FFA20E00000 IMM32.DLL
