<template>
  <div
    class="h-[calc(100vh-56px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 mt-[56px] overflow-y-auto"
  >
    <!-- Background Pattern -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -inset-10 opacity-20">
        <div
          class="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse"
        ></div>
        <div
          class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"
        ></div>
        <div
          class="absolute top-3/4 left-1/2 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-2000"
        ></div>
      </div>
    </div>

    <!-- Header Section -->
    <div class="relative z-10 pt-8 pb-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Navigation & Title -->
        <div class="flex items-center justify-between mb-8">
          <!-- Back Button -->
          <button
            @click="handleClickBack"
            class="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors duration-200 group"
          >
            <svg
              class="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <span class="font-medium">Quay lại</span>
          </button>

          <!-- Page Title -->
          <h1
            class="text-3xl font-bold text-gray-900 absolute left-1/2 transform -translate-x-1/2"
          >
            Chọn Cửa hàng
          </h1>

          <!-- Spacer to balance the layout -->
          <div class="w-24"></div>
        </div>

        <!-- Description Section -->
        <div class="text-center mb-12">
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Chọn cửa hàng bạn muốn quản lý để bắt đầu bán hàng và theo dõi hoạt
            động kinh doanh
          </p>
        </div>
      </div>
    </div>

    <!-- Stores Section -->
    <div class="relative z-10 pb-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Loading State -->
        <div
          v-if="isLoading"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          <div v-for="i in 8" :key="`skeleton-${i}`" class="animate-pulse">
            <div
              class="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg h-64"
            >
              <div class="flex items-center justify-between mb-4">
                <div class="w-16 h-16 bg-gray-300 rounded-xl"></div>
                <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
              </div>
              <div class="space-y-3">
                <div class="h-4 bg-gray-300 rounded w-3/4"></div>
                <div class="h-3 bg-gray-300 rounded w-1/2"></div>
                <div class="space-y-2 mt-4">
                  <div class="h-3 bg-gray-300 rounded w-full"></div>
                  <div class="h-3 bg-gray-300 rounded w-2/3"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Stores Grid -->
        <div
          v-else-if="useStore.dataStore?.length > 0"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          <div
            v-for="store in useStore.dataStore"
            :key="`store-${store.id}`"
            class="group"
          >
            <div
              @click="handleStore(store)"
              class="relative bg-white/80 backdrop-blur-xl rounded-2xl p-6 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer transform hover:scale-105 hover:-translate-y-2"
              :class="{
                'ring-2 ring-primary ring-opacity-50':
                  selectedStore === store.id,
              }"
            >
              <!-- Store Header -->
              <div class="flex items-center justify-between mb-4">
                <div class="relative">
                  <div
                    class="w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                  >
                    <svg
                      class="w-8 h-8 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"></path>
                      <path
                        fill-rule="evenodd"
                        d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <!-- Status Indicator -->
                  <div
                    class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center"
                  >
                    <div
                      class="w-2 h-2 bg-white rounded-full animate-pulse"
                    ></div>
                  </div>
                </div>

                <!-- Quick Actions -->
                <div
                  class="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <button
                    @click.stop="handleQuickAction(store, 'reports')"
                    class="p-2 text-gray-400 hover:text-primary transition-colors duration-200"
                    title="Xem báo cáo"
                  >
                    <svg
                      class="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
                      ></path>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Store Info -->
              <div class="mb-4">
                <h3
                  class="text-lg font-bold text-gray-900 truncate group-hover:text-primary transition-colors duration-200 mb-1"
                >
                  {{ store.name }}
                </h3>
                <p class="text-sm text-gray-500 mb-3">
                  {{ getStoreDescription(store) }}
                </p>

                <!-- Store Stats -->
                <div class="space-y-2">
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Kho hàng:</span>
                    <span class="font-medium text-gray-900">{{
                      store.warehouses?.length || 0
                    }}</span>
                  </div>
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Trạng thái:</span>
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                    >
                      <div
                        class="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"
                      ></div>
                      Hoạt động
                    </span>
                  </div>
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Loại:</span>
                    <span class="font-medium text-gray-900 capitalize">{{
                      store.type || "POS"
                    }}</span>
                  </div>
                </div>
              </div>

              <!-- Action Button -->
              <div
                class="flex items-center justify-between pt-4 border-t border-gray-100"
              >
                <div class="flex items-center space-x-2 text-sm text-gray-500">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span>Sẵn sàng</span>
                </div>

                <div
                  class="flex items-center space-x-1 text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <span class="text-sm font-medium">Truy cập</span>
                  <svg
                    class="w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-200"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>

              <!-- Loading Overlay -->
              <div
                v-if="selectedStore === store.id && isLoading"
                class="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center"
              >
                <div class="flex items-center space-x-3">
                  <LoadingSpinner />
                  <span class="text-sm font-medium text-gray-700"
                    >Đang kết nối...</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!isLoading" class="text-center py-16">
          <div
            class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <svg
              class="w-12 h-12 text-gray-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"></path>
              <path
                fill-rule="evenodd"
                d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
          <h3 class="text-xl font-medium text-gray-900 mb-2">
            Không có cửa hàng nào
          </h3>
          <p class="text-gray-500 mb-6">
            Bạn chưa được cấp quyền truy cập vào cửa hàng nào.
          </p>
          <button
            @click="handleRefresh"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-primary/90 transition-colors duration-200"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                clip-rule="evenodd"
              ></path>
            </svg>
            Làm mới
          </button>
        </div>
      </div>
    </div>

    <!-- Loading Modal with Teleport -->
    <Teleport to="body">
      <LoadingSpinner
        v-if="isLoading && selectedStore"
        text="Đang kết nối cửa hàng"
        subText="Vui lòng đợi trong giây lát..."
      />
    </Teleport>
  </div>
</template>

<script setup>
import { useHead, useRoute, useCookie, navigateTo, onMounted } from "#imports";

useHead({
  title: "Danh sách cửa hàng",
  meta: [{ name: "description", content: "Danh sách cửa hàng" }],
});

definePageMeta({
  layout: "custom",
  middleware: ["permission", "store", "auth"],
});

// Stores and composables
const useStore = useStoreStore();
const { setStore } = usePermission();
const auth = useCookie("auth");
const orderStore = useOrderStore();

// Use tab-isolated context instead of cookies
const { orgId, setStoreIdMain } = useTabContext();

// Reactive state
const isLoading = ref(false);
const selectedStore = ref(null);

// Utility functions
const getStoreDescription = (store) => {
  const warehouseCount = store.warehouses?.length || 0;
  return `Cửa hàng ${store.type || "POS"} với ${warehouseCount} kho hàng`;
};

// Enhanced handleStore with better UX
const handleStore = async (store) => {
  if (isLoading.value) return; // Prevent multiple clicks

  selectedStore.value = store.id;
  isLoading.value = true;

  try {
    // Clear previous order data
    orderStore.listOrder = [];
    if ("listOrderTerm" in orderStore) {
      orderStore.listOrderTerm = [];
    }

    // Set store data
    useCookie("warehouse").value = store.warehouses;
    await setStore(store?.id);
    await setStoreIdMain(store?.id); // Set store main
    useCookie("warehouseId").value = store.warehouseIdDefault
      ? store.warehouseIdDefault
      : store?.warehouses?.[0];

    // Navigate to features
    await navigateTo(`/feature?orgId=${orgId.value}&storeId=${store.id}`);
  } catch (error) {
    console.error("Error selecting store:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi chọn cửa hàng");
  } finally {
    isLoading.value = false;
    selectedStore.value = null;
  }
};

// Quick actions
const handleQuickAction = (store, action) => {
  switch (action) {
    case "reports":
      // Navigate to reports for this store
      navigateTo(`/report/overview?orgId=${orgId.value}&storeId=${store.id}`);
      break;
    case "manage":
      // Navigate to store management
      navigateTo(`/store-management?orgId=${orgId.value}&storeId=${store.id}`);
      break;
    default:
      console.log(`Quick action: ${action} for store:`, store.name);
  }
};

// Navigation
const handleClickBack = () => {
  navigateTo("/org");
};

// Refresh stores
const handleRefresh = async () => {
  isLoading.value = true;
  try {
    await useStore.getDataDetailStore(auth.value.user.id);
  } catch (error) {
    console.error("Error refreshing stores:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi làm mới danh sách cửa hàng");
  } finally {
    isLoading.value = false;
  }
};

// Initialize on mount
onMounted(async () => {
  isLoading.value = true;
  try {
    await useStore.getDataDetailStore(auth.value.user.id);
  } catch (error) {
    console.error("Error loading stores:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi tải danh sách cửa hàng");
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped>
/* Custom animations */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

@keyframes store-card-entrance {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Apply animations */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient-shift 15s ease infinite;
}

.group:hover .w-16.h-16 {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Store cards entrance animation */
.group {
  animation: store-card-entrance 0.6s ease-out;
}

/* Enhanced backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-xl {
    background-color: rgba(255, 255, 255, 0.9);
  }
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.8);
  }
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* Focus styles for accessibility */
.group > div:focus-visible {
  outline: 2px solid #3f51b5;
  outline-offset: 2px;
}

/* Loading overlay animation */
.absolute.inset-0 {
  backdrop-filter: blur(4px);
}

/* Removed search input styles - no longer needed */

/* Mobile optimizations */
@media (max-width: 768px) {
  .group {
    animation-duration: 0.4s;
  }

  .hover\:scale-105:hover {
    transform: scale(1.02);
  }

  .hover\:-translate-y-2:hover {
    transform: translateY(-4px);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-white\/80 {
    background-color: rgba(255, 255, 255, 0.95);
  }

  .border-white\/20 {
    border-color: rgba(0, 0, 0, 0.2);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .group {
    animation: none;
  }

  .bg-gradient-to-br {
    animation: none;
  }

  .group:hover .w-16.h-16 {
    animation: none;
  }

  .transition-all,
  .transition-colors,
  .transition-opacity,
  .transition-shadow,
  .transition-transform {
    transition: none;
  }
}
</style>
