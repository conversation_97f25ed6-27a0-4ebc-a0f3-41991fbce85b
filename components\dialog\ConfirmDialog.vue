<template>
  <!-- ✅ Optimized Modal with Teleport and Accessibility -->
  <Teleport to="body">
    <Transition name="modal" appear>
      <div
        v-if="isVisible"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4"
        @click.self="handleBackdropClick"
        role="dialog"
        aria-modal="true"
        :aria-labelledby="titleId"
        :aria-describedby="messageId"
      >
        <!-- ✅ Enhanced Modal Content with better responsive design -->
        <div
          class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
          style="contain: layout style paint"
          @click.stop
        >
          <!-- ✅ Enhanced Header with icon support -->
          <div class="px-6 pt-6 pb-4">
            <div
              class="flex items-center justify-center mb-4"
              v-if="icon || variant"
            >
              <div
                class="w-12 h-12 rounded-full flex items-center justify-center"
                :class="iconContainerClass"
              >
                <!-- Custom icon or default based on variant -->
                <component
                  :is="iconComponent"
                  class="w-6 h-6"
                  :class="iconClass"
                />
              </div>
            </div>

            <h2
              :id="titleId"
              class="text-xl font-semibold text-center text-gray-900 leading-tight"
            >
              {{ title || defaultTitle }}
            </h2>
          </div>

          <!-- ✅ Enhanced Message Content -->
          <div class="px-6 pb-6">
            <p
              :id="messageId"
              class="text-gray-600 text-center leading-relaxed"
              v-html="formattedMessage"
            ></p>
          </div>

          <!-- ✅ Enhanced Action Buttons -->
          <div class="px-6 pb-6">
            <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
              <!-- Cancel Button -->
              <button
                v-if="!isHideButton"
                ref="cancelButtonRef"
                @click="cancel"
                @keydown.enter="cancel"
                class="order-2 sm:order-1 px-4 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 focus:bg-gray-200 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2"
                :disabled="isLoading"
              >
                {{ cancelText }}
              </button>

              <!-- Confirm Button -->
              <button
                ref="confirmButtonRef"
                @click="confirm"
                @keydown.enter="confirm"
                class="order-1 sm:order-2 px-4 py-2.5 text-white rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                :class="confirmButtonClass"
                :disabled="isLoading"
              >
                <!-- Loading spinner -->
                <svg
                  v-if="isLoading"
                  class="animate-spin w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                {{ isLoading ? loadingText : confirmText }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted } from "vue";

// ✅ Enhanced Props with TypeScript support
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  message: {
    type: String,
    default: "",
  },
  isHideButton: {
    type: Boolean,
    default: false,
  },
  variant: {
    type: String,
    default: "default",
    validator: (value) =>
      ["default", "danger", "warning", "success", "info"].includes(value),
  },
  icon: {
    type: String,
    default: "",
  },
  confirmText: {
    type: String,
    default: "Đồng ý",
  },
  cancelText: {
    type: String,
    default: "Đóng",
  },
  loadingText: {
    type: String,
    default: "Đang xử lý...",
  },
  allowBackdropClose: {
    type: Boolean,
    default: true,
  },
  autoFocus: {
    type: Boolean,
    default: true,
  },
});

// ✅ Enhanced Emits
const emit = defineEmits(["confirm", "cancel", "backdrop-click"]);

// ✅ Reactive State
const isVisible = ref(true);
const isLoading = ref(false);

// ✅ Template Refs
const confirmButtonRef = ref(null);
const cancelButtonRef = ref(null);

// ✅ Unique IDs for accessibility
const titleId = computed(
  () => `confirm-dialog-title-${Math.random().toString(36).substr(2, 9)}`
);
const messageId = computed(
  () => `confirm-dialog-message-${Math.random().toString(36).substr(2, 9)}`
);

// ✅ Computed Properties
const defaultTitle = computed(() => {
  const titles = {
    danger: "Xác nhận xóa",
    warning: "Cảnh báo",
    success: "Thành công",
    info: "Thông tin",
    default: "Xác nhận",
  };
  return titles[props.variant] || titles.default;
});

const formattedMessage = computed(() => {
  return props.message.replace(/\n/g, "<br>");
});

const iconComponent = computed(() => {
  if (props.icon) return props.icon;

  const icons = {
    danger: "TrashIcon",
    warning: "ExclamationTriangleIcon",
    success: "CheckCircleIcon",
    info: "InformationCircleIcon",
    default: "QuestionMarkCircleIcon",
  };
  return icons[props.variant] || icons.default;
});

const iconContainerClass = computed(() => {
  const classes = {
    danger: "bg-red-100",
    warning: "bg-orange-100",
    success: "bg-green-100",
    info: "bg-blue-100",
    default: "bg-gray-100",
  };
  return classes[props.variant] || classes.default;
});

const iconClass = computed(() => {
  const classes = {
    danger: "text-red-600",
    warning: "text-orange-600",
    success: "text-green-600",
    info: "text-blue-600",
    default: "text-gray-600",
  };
  return classes[props.variant] || classes.default;
});

const confirmButtonClass = computed(() => {
  const classes = {
    danger: "bg-red-600 hover:bg-red-700 focus:ring-red-500",
    warning: "bg-orange-600 hover:bg-orange-700 focus:ring-orange-500",
    success: "bg-green-600 hover:bg-green-700 focus:ring-green-500",
    info: "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",
    default: "bg-primary hover:bg-primary/90 focus:ring-primary",
  };
  return classes[props.variant] || classes.default;
});

// ✅ Methods
const confirm = () => {
  if (isLoading.value) return;

  emit("confirm");
  isVisible.value = false;
};

const cancel = () => {
  if (isLoading.value) return;

  emit("cancel");
  isVisible.value = false;
};

const handleBackdropClick = () => {
  if (!props.allowBackdropClose || isLoading.value) return;

  emit("backdrop-click");
  cancel();
};

// ✅ Keyboard Navigation
const handleKeydown = (event) => {
  if (event.key === "Escape" && props.allowBackdropClose && !isLoading.value) {
    cancel();
  } else if (event.key === "Tab") {
    // Trap focus within dialog
    const focusableElements = [
      cancelButtonRef.value,
      confirmButtonRef.value,
    ].filter(Boolean);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey && document.activeElement === firstElement) {
      event.preventDefault();
      lastElement?.focus();
    } else if (!event.shiftKey && document.activeElement === lastElement) {
      event.preventDefault();
      firstElement?.focus();
    }
  }
};

// ✅ Lifecycle Hooks
onMounted(() => {
  // Add keyboard event listener
  document.addEventListener("keydown", handleKeydown);

  // Auto focus on confirm button or cancel button
  if (props.autoFocus) {
    nextTick(() => {
      const targetButton = props.isHideButton
        ? confirmButtonRef.value
        : cancelButtonRef.value;
      targetButton?.focus();
    });
  }

  // Prevent body scroll
  document.body.style.overflow = "hidden";
});

onUnmounted(() => {
  // Remove keyboard event listener
  document.removeEventListener("keydown", handleKeydown);

  // Restore body scroll
  document.body.style.overflow = "";
});

// ✅ Expose methods for parent component
defineExpose({
  confirm,
  cancel,
  setLoading: (loading) => {
    isLoading.value = loading;
  },
});
</script>

<style scoped>
/* ✅ Modal Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-to,
.modal-leave-from {
  opacity: 1;
  transform: scale(1);
}

/* ✅ Backdrop Blur Support */
@supports (backdrop-filter: blur(4px)) {
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }
}

/* ✅ Focus Trap Enhancement */
.focus-trap {
  outline: none;
}

/* ✅ Loading Animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* ✅ Button Hover Effects */
button {
  transform: translateY(0);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* ✅ Mobile Optimizations */
@media (max-width: 640px) {
  .modal-content {
    margin: 1rem;
    max-height: 90vh;
  }

  /* Stack buttons vertically on mobile */
  .flex-col {
    flex-direction: column;
  }

  /* Adjust button order for mobile UX */
  .order-1 {
    order: 1;
  }

  .order-2 {
    order: 2;
  }
}

/* ✅ High Contrast Mode Support */
@media (prefers-contrast: high) {
  .bg-white {
    background-color: white;
    border: 2px solid black;
  }

  .text-gray-600 {
    color: black;
  }
}

/* ✅ Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .modal-enter-active,
  .modal-leave-active {
    transition: opacity 0.2s ease;
  }

  .modal-enter-from,
  .modal-leave-to {
    opacity: 0;
    transform: none;
  }

  button {
    transition: none;
  }

  button:hover:not(:disabled) {
    transform: none;
  }

  .animate-spin {
    animation: none;
  }
}

/* ✅ CSS Containment for Performance */
.modal-content {
  contain: layout style paint;
}

/* ✅ Print Styles */
@media print {
  .fixed {
    position: static;
    background: white;
    color: black;
  }

  .backdrop-blur-sm {
    backdrop-filter: none;
    background: white;
  }
}
</style>
